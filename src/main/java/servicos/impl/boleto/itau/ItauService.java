package servicos.impl.boleto.itau;

import br.com.pactosolucoes.atualizadb.processo.itau.WebhookItauJSON;
import br.com.pactosolucoes.comuns.to.BoletoOnlineTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.apache.commons.lang3.StringUtils;
import org.jboleto.JBoletoBean;
import org.jboleto.bancos.Itau;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.impl.boleto.AbstractBoletoOnlineServiceComum;
import servicos.impl.boleto.AtributoBoletoEnum;
import servicos.interfaces.BoletoOnlineServiceInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.math.BigDecimal;
import java.sql.Connection;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 06/01/2022
 */
public class ItauService extends AbstractBoletoOnlineServiceComum implements BoletoOnlineServiceInterface {

    private String chaveBanco;
    private Integer codigoEmpresa;
    private Boleto boletoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Cidade cidadeDAO;
    private Empresa empresaDAO;
    private Usuario usuarioDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;

    public ItauService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        inicializarDAO(con, empresa);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    }

    private void inicializarDAO(Connection con, Integer codigoEmpresa) throws Exception {
        this.chaveBanco = DAO.resolveKeyFromConnection(con);
        this.codigoEmpresa = codigoEmpresa;
        this.boletoDAO = new Boleto(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.cidadeDAO = new Cidade(con);
        this.empresaDAO = new Empresa(con);
        this.usuarioDAO = new Usuario(con);
    }

    private String obterToken(BoletoVO boletoVO) throws Exception {
        try {
            if (UteisValidacao.emptyString(this.convenioCobrancaVO.getCodigoAutenticacao02())) {
                throw new Exception("Erro ao registrar o boleto, não foi encontrado o client_id para obter o token do itaú.");
            }
            if (UteisValidacao.emptyString(this.convenioCobrancaVO.getCodigoAutenticacao03())) {
                throw new Exception("Erro ao registrar o boleto, não foi encontrado o client_secret para obter o token do itaú.");
            }

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/x-www-form-urlencoded");

            StringBuilder body = new StringBuilder();
            body.append("&grant_type=client_credentials");
            body.append("&client_id=").append(this.convenioCobrancaVO.getCodigoAutenticacao02());
            body.append("&client_secret=").append(this.convenioCobrancaVO.getCodigoAutenticacao03());


            String caminhoCertificado = Uteis.desencriptar(this.convenioCobrancaVO.getChaveGETNET(), PropsService.getPropertyValue(PropsService.chaveDesencriptItauOnline));
            String caminhoChave = Uteis.desencriptar(this.convenioCobrancaVO.getNossaChave(), PropsService.getPropertyValue(PropsService.chaveDesencriptItauOnline));

            String ret = ExecuteRequestHttpService.executePostComCertificado((this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO) ?
                    PropsService.getPropertyValue(PropsService.urlApiItauToken) : PropsService.getPropertyValue(PropsService.urlApiItauTokenSandBox)),
                    body.toString(), headers, caminhoCertificado, caminhoChave, "pacto");
            JSONObject retorno = new JSONObject(ret);
            if (retorno.getInt("statusCode") != 200 && retorno.getInt("statusCode") != 201) {
                throw new ConsistirException("Erro ao obter token:" + retorno.optString("response") + retorno.optString("response_error"));
            }
            if (boletoVO != null) {
                boletoDAO.incluirBoletoHistorico(boletoVO, "obterToken | resposta", retorno.getString("response"));
            }
            if (!retorno.isNull("error")) {
                throw new ConsistirException("Erro ao obter token. Erro:" + retorno.getString("error"));
            }
            return "Bearer "+new JSONObject(retorno.getString("response")).getString("access_token");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public BoletoVO criar(BoletoOnlineTO boletoOnlineTO) throws Exception {
        BoletoVO boletoVO = null;
        RequestHttpService service = null;
        try {
            boletoVO = criarBoletoVO(boletoOnlineTO, this.convenioCobrancaVO, this.getCon());
            if (boletoOnlineTO.isVerificarBoletoExistente()) {
                BoletoVO boletoExistenteVO = verificarBoletoExistente(boletoVO);
                if (boletoExistenteVO != null &&
                        !UteisValidacao.emptyNumber(boletoExistenteVO.getCodigo())) {
                    return boletoExistenteVO;
                }
            }

            this.boletoDAO.incluir(boletoVO);

            EmpresaVO empresaVO = this.empresaDAO.consultarPorChavePrimaria(boletoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            PessoaCPFTO pessoaCPFTO = this.boletoDAO.obterDadosPessoaPagador(empresaVO.getCodigo(), boletoVO.getPessoaVO(), true, true);
            PessoaVO pessoaVO = this.pessoaDAO.consultarPorChavePrimaria(pessoaCPFTO.getPessoa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            CidadeVO cidadeVO = new CidadeVO();
            if (!UteisValidacao.emptyNumber(pessoaVO.getCidade().getCodigo())) {
                cidadeVO = this.cidadeDAO.consultarPorChavePrimaria(pessoaVO.getCidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            EnderecoVO enderecoVO = obterEnderecoVO(pessoaVO);

            String nossoNumero = obterNossoNumero(boletoVO);
            boletoVO.setNossoNumero(nossoNumero);

            validarDados(boletoVO);
            validarDadosObrigatorioPagador(pessoaCPFTO, cidadeVO, enderecoVO);

            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.empresa_nome, empresaVO.getNome());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.empresa_razao_social, empresaVO.getRazaoSocial());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.empresa_cnpj, this.convenioCobrancaVO.getCnpj());

            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.pagador_nome, pessoaCPFTO.getNomeResponsavel());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.pagador_documento, Uteis.formatarCpfCnpj(pessoaCPFTO.getCpfResponsavel(), true));

            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.pagador_end_logradouro, enderecoVO.getEndereco());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.pagador_end_estado_uf, cidadeVO.getEstado().getSigla());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.pagador_end_cidade, cidadeVO.getNome());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.pagador_end_numero, enderecoVO.getNumero());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.pagador_end_complemento, enderecoVO.getComplemento());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.pagador_end_bairro, enderecoVO.getBairro());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.pagador_end_cep, Formatador.removerMascara(enderecoVO.getCep()));

            JBoletoBean jBoletoBean = obterJBoletoBean(boletoVO);
            Itau itau = new Itau(jBoletoBean);

            String nossoNumeroDV = String.valueOf(itau.getDacNossoNumero());
            boletoVO.setNossoNumero(itau.getNossoNumero() + "-" + nossoNumeroDV);
            boletoVO.setLinhaDigitavel(itau.getLinhaDigitavel());

            JSONObject jsonBoleto = new JSONObject();
            jsonBoleto.put("id_boleto", boletoVO.getIdentificador()); //um campo de livre utilização pela empresa parceira
            jsonBoleto.put("etapa_processo_boleto", "efetivacao"); //validacao:teste  efetivacao:Produção
            jsonBoleto.put("codigo_canal_operacao", "API");

            JSONObject jsonBeneficiario = new JSONObject();
            jsonBeneficiario.put("id_beneficiario",new DecimalFormat("0000").format(Integer.parseInt(this.convenioCobrancaVO.getContaEmpresa().getAgencia())) +
                    new DecimalFormat("0000000").format(Integer.parseInt(this.convenioCobrancaVO.getContaEmpresa().getContaCorrente())) +
                    this.convenioCobrancaVO.getContaEmpresa().getContaCorrenteDV());
            jsonBeneficiario.put("nome_cobranca", boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_nome));
            JSONObject tipoPessoa = new JSONObject();
            tipoPessoa.put("codigo_tipo_pessoa", "J");
            jsonBeneficiario.put("tipo_pessoa", tipoPessoa);
            jsonBoleto.put("beneficiario", jsonBeneficiario);

            JSONObject dadoBoleto = new JSONObject();
            dadoBoleto.put("descricao_instrumento_cobranca", "boleto");
            dadoBoleto.put("tipo_boleto", "a vista");
            dadoBoleto.put("codigo_carteira", this.convenioCobrancaVO.getCarteira().toString());
            dadoBoleto.put("valor_total_titulo", StringUtilities.formatarCampoMonetario(boletoVO.getValor(), 15));
            dadoBoleto.put("codigo_especie", "01");
            dadoBoleto.put("data_emissao", Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
            dadoBoleto.put("indicador_pagamento_parcial", false);
            JSONObject pagador = new JSONObject();

            String nomePagador = boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_nome);
            if (nomePagador.length() > 50) {
                nomePagador = nomePagador.substring(0, 50);
            }

            String logradouroPagador = boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_end_logradouro);
            if (logradouroPagador.length() > 45) {
                logradouroPagador = logradouroPagador.substring(0, 45);
            }

            String cidadePagador = boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_end_cidade);
            if (cidadePagador.length() > 20) {
                cidadePagador = cidadePagador.substring(0, 20);
            }

            String bairroPagador = boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_end_bairro);
            if (bairroPagador.length() > 15) {
                bairroPagador = bairroPagador.substring(0, 15);
            }

            JSONObject pessoa = new JSONObject();
            pessoa.put("nome_pessoa", nomePagador);
            pessoa.put("nome_fantasia", nomePagador);
            JSONObject tipoPessoa2 = new JSONObject();
            tipoPessoa2.put("codigo_tipo_pessoa", "F");
            tipoPessoa2.put("numero_cadastro_pessoa_fisica", boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_documento));
            pessoa.put("tipo_pessoa", tipoPessoa2);
            JSONObject endereco = new JSONObject();
            endereco.put("nome_logradouro", logradouroPagador);
            endereco.put("nome_bairro", bairroPagador);
            endereco.put("nome_cidade", cidadePagador);
            endereco.put("sigla_UF", boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_end_estado_uf));
            endereco.put("numero_CEP", boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_end_cep));
            pagador.put("pessoa", pessoa);
            pagador.put("endereco", endereco);
            dadoBoleto.put("pagador", pagador);
            dadoBoleto.put("desconto_expresso", "false");
            JSONObject jsonDesconto = new JSONObject();
            jsonDesconto.put("codigo_tipo_desconto", "00"); //Quando não houver condição de desconto ? sem desconto
            JSONArray jsonDescontos = new JSONArray();
            JSONObject jsonDesc = new JSONObject();
            jsonDescontos.put(jsonDesc);
            jsonDesconto.put("descontos", jsonDescontos);
            dadoBoleto.put("desconto", jsonDesconto);
            JSONArray dadosIndividuaisBoleto = new JSONArray();
            JSONObject dadoIndividualBoleto = new JSONObject();
            if (this.convenioCobrancaVO.getCarteira().equals(109)) {
                dadoIndividualBoleto.put("numero_nosso_numero", itau.getNossoNumero());
            }
            dadoIndividualBoleto.put("data_vencimento", Calendario.getDataAplicandoFormatacao(boletoVO.getDataVencimento(), "yyyy-MM-dd"));
            dadoIndividualBoleto.put("valor_titulo", StringUtilities.formatarCampoMonetario(boletoVO.getValor(), 15));
            dadoIndividualBoleto.put("texto_seu_numero", boletoVO.getIdentificador());
            String instrucoesBoleto = this.convenioCobrancaVO.getInstrucoesBoleto();
            try {
                if (instrucoesBoleto.toUpperCase().contains("TAG_MATRICULA")) {
                    ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        instrucoesBoleto = processarInstrucaoMatricula(instrucoesBoleto, clienteVO.getMatricula());
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (instrucoesBoleto.length() > 25) {
                instrucoesBoleto = instrucoesBoleto.substring(0, 25);
            }
            dadoIndividualBoleto.put("texto_uso_beneficiario", instrucoesBoleto);
            dadosIndividuaisBoleto.put(dadoIndividualBoleto);
            dadoBoleto.put("dados_individuais_boleto", dadosIndividuaisBoleto);

            JSONObject jsonJuros = new JSONObject();
            JSONObject jsonMulta = new JSONObject();
            if (empresaVO.getCobrarAutomaticamenteMultaJuros()) {
                if (empresaVO.isUtilizarMultaValorAbsoluto()) {
                    //valor fixo

                    //juros
                    if (!UteisValidacao.emptyNumber(empresaVO.getJurosCobrancaAutomatica())) {
                        jsonJuros.put("codigo_tipo_juros", "93"); // Valor diário (utilizando parâmetros do cadastro de beneficiário para dias úteis ou corridos)

                        String jurosEmpresa = String.valueOf(Uteis.arredondarForcando2CasasDecimais(empresaVO.getJurosCobrancaAutomatica()));
                        String inteiroJuros = jurosEmpresa.split("\\.")[0];
                        String decimalJuros = jurosEmpresa.split("\\.")[1];
                        String inteiroJurosAjustado = StringUtils.leftPad(inteiroJuros, 15, "0");
                        String decimalJurosAjustado = StringUtils.rightPad(decimalJuros, 2, "0");
                        jsonJuros.put("valor_juros", inteiroJurosAjustado + decimalJurosAjustado);
                    } else {
                        jsonJuros.put("codigo_tipo_juros", "05"); // Quando não se deseja cobrar juros caso o pagamento seja feito após o vencimento (isento)
                    }

                    //multa
                    if (!UteisValidacao.emptyNumber(empresaVO.getMultaCobrancaAutomatica())) {
                        jsonMulta.put("codigo_tipo_multa", "01"); //Quando se deseja cobrar um valor fixo de multa após o vencimento.

                        String multaEmpresa = String.valueOf(Uteis.arredondarForcando2CasasDecimais(empresaVO.getMultaCobrancaAutomatica()));
                        String inteiroMulta = multaEmpresa.split("\\.")[0];
                        String decimalMulta = multaEmpresa.split("\\.")[1];
                        String inteiroMultaAjustado = StringUtils.leftPad(inteiroMulta, 15, "0");
                        String decimalMultaAjustado = StringUtils.rightPad(decimalMulta, 2, "0");
                        jsonMulta.put("valor_multa", inteiroMultaAjustado + decimalMultaAjustado);
                    } else {
                        jsonMulta.put("codigo_tipo_multa", "03"); // Quando não se deseja cobrar multa caso o pagamento seja feito após o vencimento (isento)
                    }
                } else {
                    //percentual

                    //juros
                    if (!UteisValidacao.emptyNumber(empresaVO.getJurosCobrancaAutomatica())) {
                        jsonJuros.put("codigo_tipo_juros", "91"); //Percentual diário (utilizando parâmetros do cadastro de beneficiário para dias úteis ou corridos)

                        String jurosEmpresa = String.valueOf(Uteis.arredondarForcando2CasasDecimais(empresaVO.getJurosCobrancaAutomatica()));
                        String inteiroJuros = jurosEmpresa.split("\\.")[0];
                        String decimalJuros = jurosEmpresa.split("\\.")[1];
                        String inteiroJurosAjustado = StringUtils.leftPad(inteiroJuros, 7, "0");
                        String decimalJurosAjustado = StringUtils.rightPad(decimalJuros, 5, "0");

                        jsonJuros.put("percentual_juros", inteiroJurosAjustado + decimalJurosAjustado); // Não se aplica juros caso o título seja pago após a Data de Vencimento (isento)
                    } else {
                        jsonJuros.put("codigo_tipo_juros", "05"); // Quando não se deseja cobrar juros caso o pagamento seja feito após o vencimento (isento)
                    }

                    //multa
                    if (!UteisValidacao.emptyNumber(empresaVO.getMultaCobrancaAutomatica())) {
                        jsonMulta.put("codigo_tipo_multa", "02"); //Quando se deseja cobrar um percentual do valor do título de multa após o vencimento

                        String multaEmpresa = String.valueOf(Uteis.arredondarForcando2CasasDecimais(empresaVO.getMultaCobrancaAutomatica()));
                        String inteiroMulta = multaEmpresa.split("\\.")[0];
                        String decimalMulta = multaEmpresa.split("\\.")[1];
                        String inteiroMultaAjustado = StringUtils.leftPad(inteiroMulta, 7, "0");
                        String decimalMultaAjustado = StringUtils.rightPad(decimalMulta, 5, "0");
                        jsonMulta.put("percentual_multa", inteiroMultaAjustado + decimalMultaAjustado);
                    } else {
                        jsonMulta.put("codigo_tipo_multa", "03"); // Quando não se deseja cobrar multa caso o pagamento seja feito após o vencimento (isento)
                    }
                }
            } else {
                jsonJuros.put("codigo_tipo_juros", "05"); // Quando não se deseja cobrar juros caso o pagamento seja feito após o vencimento (isento)
                jsonMulta.put("codigo_tipo_multa", "03"); // Quando não se deseja cobrar multa caso o pagamento seja feito após o vencimento (isento)
            }

            dadoBoleto.put("juros", jsonJuros);
            dadoBoleto.put("multa", jsonMulta);

            JSONObject jsonRecebimentoDivergente = new JSONObject();
            jsonRecebimentoDivergente.put("codigo_tipo_autorizacao", "03"); // Quando o título não deve aceitar pagamentos de valores divergentes ao da cobrança
            dadoBoleto.put("recebimento_divergente", jsonRecebimentoDivergente);

            jsonBoleto.put("dado_boleto", dadoBoleto);

            try {
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");
                headers.put("Authorization", obterToken(boletoVO));
                headers.put("x-itau-apikey", this.convenioCobrancaVO.getCodigoAutenticacao02());
                headers.put("x-itau-correlationID", Uteis.formatarCpfCnpj(this.convenioCobrancaVO.getCnpj(), true));
                headers.put("x-itau-flowID", "emitirBoleto1");

                JSONObject jsonEnviar = new JSONObject();
                jsonEnviar.put("data", jsonBoleto);
                boletoVO.setParamsEnvio(jsonEnviar.toString());

                String caminhoCertificado = Uteis.desencriptar(this.convenioCobrancaVO.getChaveGETNET(), PropsService.getPropertyValue(PropsService.chaveDesencriptItauOnline));
                String caminhoChave = Uteis.desencriptar(this.convenioCobrancaVO.getNossaChave(), PropsService.getPropertyValue(PropsService.chaveDesencriptItauOnline));

                String ret = ExecuteRequestHttpService.executePostComCertificado((this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO) ?
                        PropsService.getPropertyValue(PropsService.urlApiItauRegistro) :
                        PropsService.getPropertyValue(PropsService.urlApiItauRegistroSandBox))+"/boletos",
                jsonEnviar.toString(), headers, caminhoCertificado, caminhoChave, "pacto");

                JSONObject jsonRetorno = new JSONObject(ret);
                JSONObject response = !UteisValidacao.emptyString(jsonRetorno.optString("response")) ? new JSONObject(jsonRetorno.optString("response")) : new JSONObject();
                boletoDAO.incluirBoletoHistorico(boletoVO, "criar | resposta", jsonRetorno.toString());
                boletoVO.setParamsResposta(jsonRetorno.toString());

                if (response.optJSONObject("data") != null && !response.optJSONObject("data").isNull("beneficiario")) {

                    // boleto registrado com sucesso.
                    System.out.println("BOLETO REGISTRADO COM SUCESSO. JSON:" + response);
                    JSONObject dadosBoleto = new JSONObject(response.getJSONObject("data").getJSONObject("dado_boleto").getJSONArray("dados_individuais_boleto").get(0).toString());
                    if (!jsonBoleto.getString("etapa_processo_boleto").equals("validacao")) {
                        boletoVO.setIdExterno(dadosBoleto.getString("id_boleto_individual"));
                    }
                    boletoVO.setNossoNumero(dadosBoleto.getString("numero_nosso_numero"));
                    boletoVO.setNumeroInterno(dadosBoleto.getString("numero_nosso_numero"));
                    boletoVO.setNumeroExterno(dadosBoleto.getString("numero_nosso_numero"));
                    boletoVO.setLinhaDigitavel(dadosBoleto.getString("numero_linha_digitavel"));
                    boletoVO.setCodigoBarrasNumerico(dadosBoleto.getString("codigo_barras"));
                    boletoVO.setLinkBoleto(boletoVO.obterUrlLinkPDF(this.chaveBanco));
                    boletoVO.setSituacao(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO);
                } else {
                    // erro ao registrar o boleto.
                    String mensagem = "";
                    String codigo = "";
                    String motivo = "";
                    if (!UteisValidacao.emptyString(jsonRetorno.optString("statusCode"))) {
                        codigo = jsonRetorno.optString("statusCode") + " - ";
                        if (jsonRetorno.optString("statusCode").equals("400")) {
                            mensagem = "Parâmetros incorretos";
                        } else if (jsonRetorno.optString("statusCode").equals("401")) {
                            mensagem = "Não autorizado";
                        } else if (jsonRetorno.optString("statusCode").equals("403")) {
                            mensagem = "Acesso proibido";
                        } else if (jsonRetorno.optString("statusCode").equals("404")) {
                            mensagem = "Recurso inexistente";
                        } else if (jsonRetorno.optString("statusCode").equals("405")) {
                            mensagem = "Método não permitido";
                        } else if (jsonRetorno.optString("statusCode").equals("422")) {
                            mensagem = "Dados informados estão fora do escopo definido para o campo";
                        } else if (jsonRetorno.optString("statusCode").equals("428")) {
                            mensagem = "Pré-requisito necessário";
                        } else if (jsonRetorno.optString("statusCode").equals("500")) {
                            mensagem = "Erro inesperado";
                        } else if (jsonRetorno.optString("statusCode").equals("501")) {
                            mensagem = "Não implementado";
                        } else if (jsonRetorno.optString("statusCode").equals("503")) {
                            mensagem = "Serviço indisponível";
                        } else if (jsonRetorno.optString("statusCode").equals("504")) {
                            mensagem = "Time Out, consulte em instantes se seu boleto foi emitido";
                        }
                        if (!new JSONObject(jsonRetorno.optString("response_error")).has("data")) {
                            motivo = jsonRetorno.optString("response_error");
                        }
                    }

                    throw new ConsistirException(codigo + mensagem + ". Retorno: " + (UteisValidacao.emptyString(motivo) ? "Não especificado pela API do Itaú" : motivo));
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.msg_erro, ex.getMessage());
                boletoVO.setSituacao(SituacaoBoletoEnum.ERRO);
            }

            this.boletoDAO.alterar(boletoVO);
            return boletoVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarBoletoComErro(boletoVO, ex);
            throw ex;
        } finally {
            service = null;
        }
    }

    public String montarUrlCallBackEnviarBanco() throws Exception {
        JSONObject jsonEviar = new JSONObject();
        JSONObject jsonDados = new JSONObject();
        jsonDados.put("id_beneficiario", new DecimalFormat("0000").format(Integer.parseInt(this.convenioCobrancaVO.getContaEmpresa().getAgencia())) +
                new DecimalFormat("0000000").format(Integer.parseInt(this.convenioCobrancaVO.getContaEmpresa().getContaCorrente())) +
                this.convenioCobrancaVO.getContaEmpresa().getContaCorrenteDV());
        jsonDados.put("webhook_url", "https://app.pactosolucoes.com.br/api/prest/boleto/itau");
        jsonDados.put("webhook_client_id", this.convenioCobrancaVO.getCodigoAutenticacao02());
        jsonDados.put("webhook_client_secret", this.convenioCobrancaVO.getCodigoAutenticacao03());
        jsonDados.put("webhook_oauth_url", "https://app.pactosolucoes.com.br/api/prest/boleto/itau/"+this.codigoEmpresa+"/"+this.chaveBanco+"/auth");
        jsonDados.put("valor_minimo", 0.01);
        jsonDados.put("tipos_notificacoes", new String[]{"BAIXA_EFETIVA", "BAIXA_OPERACIONAL"});
        jsonEviar.put("data", jsonDados);

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", obterToken(null));
        headers.put("x-itau-apikey", this.convenioCobrancaVO.getCodigoAutenticacao02());
        headers.put("x-itau-correlationID", Uteis.formatarCpfCnpj(this.convenioCobrancaVO.getCnpj(), true));
        headers.put("x-itau-flowID", "enviarwebhook");

        String caminhoCertificado = Uteis.desencriptar(this.convenioCobrancaVO.getChaveGETNET(), PropsService.getPropertyValue(PropsService.chaveDesencriptItauOnline));
        String caminhoChave = Uteis.desencriptar(this.convenioCobrancaVO.getNossaChave(), PropsService.getPropertyValue(PropsService.chaveDesencriptItauOnline));

//        String ret = ExecuteRequestHttpService.executePostComCertificado("https://boletos.cloud.itau.com.br/boletos/v3/notificacoes_boletos",
//                jsonEviar.toString(), headers, caminhoCertificado, caminhoChave, "pacto");

//        if (new JSONObject(ret).getInt("statusCode") == 201) {
//            this.boletoDAO.incluirHistoricoWebHookBoleto(this.convenioCobrancaVO.getCodigo(), this.codigoEmpresa, ret);
//        }

        return null;
    }

    public String consultarRetornoCallBackInserido() {
        try {
                String idBeneficiario = new DecimalFormat("0000").format(Integer.parseInt(this.convenioCobrancaVO.getContaEmpresa().getAgencia())) +
                    new DecimalFormat("0000000").format(Integer.parseInt(this.convenioCobrancaVO.getContaEmpresa().getContaCorrente())) +
                    this.convenioCobrancaVO.getContaEmpresa().getContaCorrenteDV();

                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");
                headers.put("Authorization", obterToken(null));
                headers.put("x-itau-apikey", this.convenioCobrancaVO.getCodigoAutenticacao02());
                headers.put("x-itau-correlationID", Uteis.formatarCpfCnpj(this.convenioCobrancaVO.getCnpj(), true));
//                headers.put("x-itau-flowID", "consultarwebhook");

                String caminhoCertificado = Uteis.desencriptar(this.convenioCobrancaVO.getChaveGETNET(), PropsService.getPropertyValue(PropsService.chaveDesencriptItauOnline));
                String caminhoChave = Uteis.desencriptar(this.convenioCobrancaVO.getNossaChave(), PropsService.getPropertyValue(PropsService.chaveDesencriptItauOnline));

                String ret = ExecuteRequestHttpService.executeComCertificado((this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO) ?
                                PropsService.getPropertyValue(PropsService.urlApiItauWebhook) + "/notificacoes_boletos?id_beneficiario=" + idBeneficiario : ""),
                        null, headers, caminhoCertificado, caminhoChave, "pacto", "GET");

                JSONObject jsonRetorno = new JSONObject(ret);
                JSONObject response = !UteisValidacao.emptyString(jsonRetorno.optString("response")) ? new JSONObject(jsonRetorno.optString("response")) : new JSONObject();

                System.out.println("RETORNO: " + response);
            return "";
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public String obterNossoNumero(BoletoVO boletoVO) {
        return StringUtilities.formatarCampo(new BigDecimal(boletoVO.getIdentificador().toString()), 8);
    }

    public JBoletoBean obterJBoletoBean(BoletoVO boletoVO) {
        JBoletoBean jBoletoBean = new JBoletoBean();
        String numAgencia = this.convenioCobrancaVO.getContaEmpresa().getAgencia();
        String dvAg = this.convenioCobrancaVO.getContaEmpresa().getAgenciaDV();
        jBoletoBean.setAgencia(numAgencia);
        jBoletoBean.setDvAgencia(dvAg);
        jBoletoBean.setNumConvenio(this.convenioCobrancaVO.getNumeroContrato());

        String numConta = this.convenioCobrancaVO.getContaEmpresa().getContaCorrente();
        String dvConta = this.convenioCobrancaVO.getContaEmpresa().getContaCorrenteDV();
        jBoletoBean.setContaCorrente(numConta);
        jBoletoBean.setDvContaCorrente(dvConta);
        jBoletoBean.setCarteira(this.convenioCobrancaVO.getCarteira().toString());
        jBoletoBean.setNossoNumero(obterNossoNumero(boletoVO));
        jBoletoBean.setMoeda("9");
        jBoletoBean.setAceite("N");
        jBoletoBean.setEspecieDocumento("DS");
        jBoletoBean.setValorBoleto(Formatador.formatarValorMonetarioSemMoeda(boletoVO.getValor()));
        jBoletoBean.setDataProcessamento(Calendario.getData(Calendario.hoje(), "dd/MM/yyyy"));
        jBoletoBean.setDataDocumento(Calendario.getData(boletoVO.getDataRegistro(), "dd/MM/yyyy"));
        jBoletoBean.setDataVencimento(Calendario.getData(boletoVO.getDataVencimento(), "dd/MM/yyyy"));
        jBoletoBean.setNumConvenio(StringUtilities.formatarCampoForcandoZerosAEsquerda(this.convenioCobrancaVO.getNumeroContrato(), 7));
        jBoletoBean.setNomeSacado(boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_nome));
        jBoletoBean.setCpfSacado(boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_documento));
        jBoletoBean.setLocalPagamento("PAGAVEL EM QUALQUER BANCO ATE O VENCIMENTO");
        jBoletoBean.setLocalPagamento2("");

        String enderecoLogradouro = boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_end_logradouro);
        String enderecoNumero = boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_end_numero);
        String enderecoComplemento = boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_end_complemento);

        StringBuilder cedente = new StringBuilder();
        cedente.append(boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.empresa_nome));
        cedente.append(" - CPNJ: ").append(boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.empresa_cnpj));

        if (!UteisValidacao.emptyString(enderecoLogradouro)) {
            cedente.append(" - End.: ").append(enderecoLogradouro);
        }
        if (!UteisValidacao.emptyString(enderecoNumero)) {
            cedente.append(", ").append(enderecoNumero);
        }
        if (!UteisValidacao.emptyString(enderecoComplemento)) {
            cedente.append(", ").append(enderecoComplemento);
        }
        jBoletoBean.setCedente(cedente.toString());

        StringBuilder enderecoSb = new StringBuilder();
        enderecoSb.append(enderecoLogradouro);
        if (!UteisValidacao.emptyString(enderecoNumero)) {
            enderecoSb.append(", ").append(enderecoNumero);
        }
        if (!UteisValidacao.emptyString(enderecoComplemento)) {
            enderecoSb.append(", ").append(enderecoComplemento);
        }
        jBoletoBean.setEnderecoSacado(enderecoSb.toString());
        jBoletoBean.setBairroSacado(boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_end_bairro));
        jBoletoBean.setCepSacado(boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_end_cep));
        jBoletoBean.setUfSacado(boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_end_estado_uf));
        jBoletoBean.setCidadeSacado(boletoVO.obterItemOutrasInformacoes(AtributoBoletoEnum.pagador_end_cidade));
        jBoletoBean.setCodCliente(this.convenioCobrancaVO.getNumeroContrato());
        jBoletoBean.setNoDocumento(boletoVO.getIdentificador().toString());
        return jBoletoBean;
    }

    private void validarDadosObrigatorioPagador(PessoaCPFTO pessoaCPFTO, CidadeVO cidadeVO, EnderecoVO enderecoVO) throws Exception {
        if (UteisValidacao.emptyString(pessoaCPFTO.getCpfResponsavel())) {
            throw new ConsistirException("O CPF/CNPJ do cliente é obrigatório para registrar o boleto no site do itaú.");
        }
        if (UteisValidacao.emptyString(pessoaCPFTO.getNomeResponsavel())) {
            throw new ConsistirException("O nome do cliente é obrigatório para registrar o boleto no site do itaú.");
        }

        if (enderecoVO == null) {
            throw new ConsistirException("O endereço do cliente é obrigatório para registrar o boleto no site do itaú.");
        }

        if (UteisValidacao.emptyString(enderecoVO.getEndereco())) {
            throw new ConsistirException("O endereço do cliente é obrigatório para registrar o boleto no site do itaú.");
        }

        if (cidadeVO == null) {
            throw new ConsistirException("A cidade do cliente é obrigatório para registrar o boleto no site do itaú.");
        }

        if (UteisValidacao.emptyNumber(cidadeVO.getCodigo()) || UteisValidacao.emptyString(cidadeVO.getNome())) {
            throw new ConsistirException("A cidade do cliente é obrigatório para registrar o boleto no site do itaú.");
        }

        if (UteisValidacao.emptyString(cidadeVO.getEstado().getSigla())) {
            throw new ConsistirException("O estado(UF) do cliente é obrigatório para registrar o boleto no site do itaú.");
        }
        if (UteisValidacao.emptyString(enderecoVO.getCep())) {
            throw new ConsistirException("O CEP do cliente é obrigatório para registrar o boleto no site do itaú.");
        }
    }

    private void validarDados(BoletoVO boletoVO) throws Exception {
        if (UteisValidacao.emptyString(this.chaveBanco)) {
            throw new ConsistirException("Operação não permitida, não foi possível identificar a chaveZW.");
        }

        if (UteisValidacao.emptyNumber(this.convenioCobrancaVO.getCarteira())) {
            throw new Exception("O parâmetro carteira é obrigatório para registrar o boleto no site do itaú.");
        }
        if (UteisValidacao.emptyString(boletoVO.getNossoNumero())) {
            throw new ConsistirException("O parâmetro nossoNumero é obrigatório para registrar o boleto no site do itaú.");
        }
//        if ((dvNossoNumero == null) || (dvNossoNumero.trim().equals(""))){
//            throw new ConsistirException("O parâmetro dvNossoNumero é obrigatório para registrar o boleto no site do itaú.");
//        }
        if (boletoVO.getDataVencimento() == null) {
            throw new ConsistirException("O parâmetro dataVencimento é obrigatório para registrar o boleto no site do itaú.");
        }
        if (UteisValidacao.emptyNumber(boletoVO.getValor())) {
            throw new ConsistirException("O parâmetro valorCobrado é obrigatório para registrar o boleto no site do itaú.");
        }
        if (UteisValidacao.emptyString(this.convenioCobrancaVO.getCnpj())) {
            throw new ConsistirException("O parâmetro cnpjBeneficiario é obrigatório para registrar o boleto no site do itaú.");
        }
        if (UteisValidacao.emptyString(this.convenioCobrancaVO.getContaEmpresa().getAgencia())) {
            throw new ConsistirException("O parâmetro agenciaBeneficiario é obrigatório para registrar o boleto no site do itaú.");
        }
        if (UteisValidacao.emptyString(this.convenioCobrancaVO.getContaEmpresa().getContaCorrente())) {
            throw new ConsistirException("O parâmetro contaCorrenteBeneficiario é obrigatório para registrar o boleto no site do itaú.");
        }
        if (UteisValidacao.emptyString(this.convenioCobrancaVO.getContaEmpresa().getContaCorrenteDV())) {
            throw new ConsistirException("O parâmetro digitoVerificadorContaBeneficiario é obrigatório para registrar o boleto no site do itaú.");
        }
    }

    private String processarInstrucaoMatricula(String instrucao, String matriculaCliente) throws Exception {
        if (instrucao.contains("<matricula>")) {
            int inicioMatricula = instrucao.indexOf("<matricula>");
            int finalMatricula = instrucao.indexOf("</matricula>");
            String aux = instrucao.substring(0, inicioMatricula);
            aux = aux + instrucao.substring((inicioMatricula + 11), finalMatricula);
            aux = aux.replaceAll("TAG_MATRICULA", matriculaCliente != null ? matriculaCliente : "");
            aux += instrucao.substring(finalMatricula + 12);
            instrucao = aux;
        }
        return instrucao;
    }

    @Override
    public void cancelar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao, boolean origemProcessoManutencao) throws Exception {
//        Recurso não disponível para ITAU.
//        Então somente alterar a situação do boleto no sistema
        Boleto boletoDAO;
        try {
            boletoDAO = new Boleto(this.getCon());
            String operacaoGravar = ("Cancelamento de Boleto - " + boletoVO.getCodigo());
            JSONObject jsonEstorno = boletoDAO.gerarBaseJSON(usuarioVO, operacaoGravar);
            boletoVO.setJsonEstorno(jsonEstorno.toString());
            boletoDAO.alterarJsonEstorno(boletoVO);
            boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);
        } finally {
            boletoDAO = null;
        }
    }

    @Override
    public void processarWebhook(BoletoVO boletoVO, String json) throws Exception {
        processarBoletoPJBank(boletoVO, json, this.usuarioDAO.getUsuarioRecorrencia(), "processarWebhook");
    }

    private SituacaoBoletoEnum obterSituacaoBoleto(WebhookItauJSON webhookItauJSON) {
        SituacaoBoletoEnum situacaoBoletoEnum = null;
        String statusItau = webhookItauJSON.getTipoLiquidacao();
        if (statusItau.equals(WebhookItauJSON.BAIXA_OPERACIONAL)) {
            situacaoBoletoEnum = SituacaoBoletoEnum.CANCELADO;
        } else if (statusItau.equals(WebhookItauJSON.LIQUIDACAO_NORMAL)) {
            situacaoBoletoEnum = SituacaoBoletoEnum.PAGO;
        }
        return situacaoBoletoEnum;
    }

    @Override
    public void sincronizar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao) throws Exception {
        throw new Exception("Recurso não disponível para ITAU.");
    }

    private void processarBoletoPJBank(BoletoVO boletoVO, String json, UsuarioVO usuarioVO, String operacao) throws Exception {
        Boleto boletoDAO = null;
        BoletoVO boletoVOAnterior = null;
        try {
            boletoDAO = new Boleto(this.getCon());
            boletoDAO.incluirBoletoHistorico(boletoVO, "processarBoletoItau | " + operacao, json);

            WebhookItauJSON webhookItauJSON = new WebhookItauJSON(new JSONObject(json));

            if (boletoVO == null) {
                if (UteisValidacao.emptyString(webhookItauJSON.getIdBoleto())) {
                    throw new Exception("JSON Boleto Itau não tem IdBoleto informado");
                }
                boletoVO = boletoDAO.consultarPorIdexternoTipo(webhookItauJSON.getIdBoleto(), TipoBoletoEnum.ITAU, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (boletoVO == null) {
                throw new Exception("Boleto não encontrado");
            }

            boletoVOAnterior = (BoletoVO) boletoVO.getClone(true);

            //em caso de webhook por baixa operacional
            if (webhookItauJSON.getTipoLiquidacao().equals(WebhookItauJSON.BAIXA_OPERACIONAL)) {
                if (!boletoVO.getSituacao().equals(SituacaoBoletoEnum.CANCELADO)) {
                    //boleto já está cancelado
                    return;
                }
                if (!UteisValidacao.emptyNumber(boletoVO.getMovPagamentoVO().getCodigo()) ||
                        !UteisValidacao.emptyNumber(boletoVO.getReciboPagamentoVO().getCodigo())) {
                    throw new Exception("Boleto está pago por isso não é possível alterar para cancelado. Boleto " + boletoVO.getCodigo());
                }
                boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);
                return;
            }


            //obter situacao conforme a os dados recebidos
            SituacaoBoletoEnum situacaoBoletoEnum = obterSituacaoBoleto(webhookItauJSON);
            boletoVO.setSituacao(situacaoBoletoEnum);
            boletoVO.setValorPago(Double.valueOf(webhookItauJSON.getValor_pago_Float()));
            boletoVO.setValorLiquido(Double.valueOf(webhookItauJSON.getValor_liquido_Float()));
            boletoVO.setDataPagamento(webhookItauJSON.getData_pagamento_Date());
            boletoVO.setDataCredito(webhookItauJSON.getData_credito_Date());

            if (webhookItauJSON.getValor_pago_Float() > 0.0) {
                if (!UteisValidacao.emptyNumber(boletoVO.getReciboPagamentoVO().getCodigo()) ||
                        !UteisValidacao.emptyNumber(boletoVO.getMovPagamentoVO().getCodigo())) {
                    boletoDAO.incluirBoletoHistorico(boletoVO, operacao, "BOLETO JÁ ESTÁ PAGO");
                } else {
                    //gerar recibo pagamento
                    gerarPagamentoBoleto(boletoVO, usuarioVO, true, false);
                }
            }

            //atualizar dados do boleto
            boletoDAO.alterar(boletoVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            if (boletoDAO != null) {
                boletoDAO.incluirBoletoHistorico(boletoVO, operacao + " | processarBoletoItau | ERRO", ex.getMessage());
            }
            throw ex;
        } finally {
            if (boletoVOAnterior != null && boletoVO != null) {
                if (!boletoVOAnterior.getSituacao().equals(boletoVO.getSituacao())) {
                    JSONObject jsonSituacao = new JSONObject();
                    jsonSituacao.put("situacao_anterior", boletoVOAnterior.getSituacao().getDescricao());
                    jsonSituacao.put("situacao_atual", boletoVO.getSituacao().getDescricao());
                    boletoDAO.incluirBoletoHistorico(boletoVO, "processarBoletoItau - ALTERAR SITUAÇÃO", jsonSituacao.toString());
                }
            }

            boletoDAO = null;
        }
    }
}
